'use client'

import { useState, useEffect, useRef } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Progress } from "@/components/ui/progress"
import { Copy, ChevronDown, ChevronUp, RefreshCw, AlertCircle } from 'lucide-react'
import { useToast } from "@/hooks/use-toast"
import ReactMarkdown from 'react-markdown'

function useAutoResizeTextArea(value: string) {
  const ref = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    if (ref.current) {
      ref.current.style.height = 'auto';
      ref.current.style.height = `${Math.min(ref.current.scrollHeight, 300)}px`;
    }
  }, [value]);

  return ref;
}

// 定义消息接口
interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

interface ConversationTurn {
  prompt: string;
  step2Prompt: string;
  step3Prompt: string;
  results: Array<{ url: string; title: string; content: string; step: number; error?: string; conversation_history?: ChatMessage[] }>;
}

function CollapsibleText({ content }: { content: string }) {
  const [isExpanded, setIsExpanded] = useState(false)

  return (
    <div>
      <div className={`overflow-hidden ${isExpanded ? '' : 'max-h-24'}`}>
        <ReactMarkdown>{content}</ReactMarkdown>
      </div>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsExpanded(!isExpanded)}
        className="mt-2"
      >
        {isExpanded ? (
          <>
            收起 <ChevronUp className="ml-2 h-4 w-4" />
          </>
        ) : (
          <>
            展开 <ChevronDown className="ml-2 h-4 w-4" />
          </>
        )}
      </Button>
    </div>
  )
}

// 在组件顶部添加新的函数来处理数据的本地存储
const saveInputsToLocalStorage = (data: {
  urls: string;
  prompt: string;
  step2Prompt: string;
  step3Prompt: string;
  selectedModel: string;
}) => {
  localStorage.setItem('savedInputs', JSON.stringify(data));
}

const loadInputsFromLocalStorage = () => {
  const saved = localStorage.getItem('savedInputs');
  return saved ? JSON.parse(saved) : null;
}

// 添加延迟函数
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// 添加带重试的 fetch 函数
const fetchWithRetry = async (
  url: string, 
  options: RequestInit, 
  retries = 3, 
  delayMs = 5000
) => {
  for (let i = 0; i < retries; i++) {
    try {
      // 每次调用前等待
      await delay(delayMs);
      
      const response = await fetch(url, options);
      const data = await response.json();
      
      if (data.status === 'success' || data.result) {
        return data;
      }
      
      throw new Error(data.message || 'API调用失败');
    } catch (error) {
      if (i === retries - 1) throw error;
      console.log(`第${i + 1}次调用失败，5秒后重试...`);
      await delay(delayMs);
    }
  }
};

export default function Component() {
  const [urls, setUrls] = useState('')
  const [prompt, setPrompt] = useState('')
  const [step2Prompt, setStep2Prompt] = useState('')
  const [step3Prompt, setStep3Prompt] = useState('')
  const [conversation, setConversation] = useState<ConversationTurn[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [sessionId, setSessionId] = useState('')
  const [selectedModel, setSelectedModel] = useState('gpt-4o')
  const [progress, setProgress] = useState(0)
  const [currentUrlIndex, setCurrentUrlIndex] = useState(0)
  const [currentStep, setCurrentStep] = useState('')
  const [elapsedTime, setElapsedTime] = useState(0)
  const { toast } = useToast()
  const urlsRef = useAutoResizeTextArea(urls)
  const [shouldStop, setShouldStop] = useState(false)

  // 添加加载保存的输入数据的 useEffect
  useEffect(() => {
    const savedInputs = loadInputsFromLocalStorage();
    if (savedInputs) {
      setUrls(savedInputs.urls);
      setPrompt(savedInputs.prompt);
      setStep2Prompt(savedInputs.step2Prompt);
      setStep3Prompt(savedInputs.step3Prompt);
      setSelectedModel(savedInputs.selectedModel);
      // 清除存储的数据，防止刷新后重复加载
      localStorage.removeItem('savedInputs');
    }
  }, []);

  useEffect(() => {
    setSessionId(Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15))
  }, [])

  // 修改 processUrl 函数以支持对话历史
  const processUrl = async (
    url: string,
    step: number,
    title: string = '',
    content: string = '',
    conversationHistory: ChatMessage[] = []
  ) => {
    if (shouldStop) {
      throw new Error('操作已被用户终止');
    }

    // 只在第一步时调用提取接口
    if (step === 1) {
      setCurrentStep(`获取内容 (步骤 ${step})`);

      // 根据URL选择合适的API端点
      let apiEndpoint = '/api/tiqu';  // 默认使用tiqu接口

      if (url.startsWith('https://mp.weixin.qq.com')) {
        apiEndpoint = '/api/tiqu';
      } else if (url.startsWith('https://www.toutiao.com')) {
        apiEndpoint = '/api/tiqu_toutiao';
      } else if (url.startsWith('https://mparticle.uc.cn')) {
        apiEndpoint = '/api/tiqu_uc';
      } else if (url.startsWith('https://www.xiaohongshu.com')) {
        apiEndpoint = '/api/tiqu_xhs';
      }

      // 使用带重试的 fetch 调用提取接口
      const tiquData = await fetchWithRetry(
        apiEndpoint,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ url })
        }
      );

      if (tiquData.status !== 'success') {
        throw new Error('Failed to fetch content');
      }

      content = tiquData.data;
      title = tiquData.title;
    }

    setCurrentStep(`生成内容 (步骤 ${step})`);

    // 使用带重试的 fetch 调用聊天接口
    const chatData = await fetchWithRetry(
      '/api/chat',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          session_id: sessionId,
          step: step,
          model: selectedModel,
          prompt: step === 1 ? prompt : step === 2 ? step2Prompt : step3Prompt,
          conversation_history: conversationHistory, // 传递对话历史
          variables: {
            title: title,
            标题: title,
            author: "AI作者",
            作者: "AI作者",
            publish_date: new Date().toISOString().split('T')[0],
            发布时间: new Date().toISOString().split('T')[0],
            content: content,
            原文: content,
            framework: "",
            框架: ""
          }
        })
      }
    );

    return {
      url,
      title: title,
      content: content,
      step: step,
      result: chatData.result.text,
      conversation_history: chatData.conversation_history // 返回更新后的对话历史
    };
  };

  const handleSubmit = async () => {
    setIsLoading(true)
    setProgress(0)
    setCurrentUrlIndex(0)
    setCurrentStep('')
    setElapsedTime(0)
    setShouldStop(false)
    const startTime = Date.now()
    const timer = setInterval(() => {
      setElapsedTime(Math.floor((Date.now() - startTime) / 1000))
    }, 1000)
    const urlList = urls.split('\n').filter(url => url.trim() !== '')
    const totalUrls = urlList.length

    try {
      for (let i = 0; i < totalUrls; i++) {
        if (shouldStop) {
          return; // 立即退出函数
        }
        const url = urlList[i]
        try {
          setCurrentUrlIndex(i + 1)
          
          // 创建一个组存储所有步骤的结果
          const results: { url: string; title: string; content: string; step: number; conversation_history?: ChatMessage[]}[] = []

          // 初始化对话历史
          let currentConversationHistory: ChatMessage[] = []

          // Step 1
          const step1Result = await processUrl(url, 1, '', '', currentConversationHistory)
          currentConversationHistory = step1Result.conversation_history || []
          results.push({
            url: step1Result.url,
            title: step1Result.title,
            content: step1Result.result,
            step: step1Result.step,
            conversation_history: currentConversationHistory
          });

          // Step 2 (if prompt is provided)
          if (step2Prompt) {
            const step2Result = await processUrl(url, 2, step1Result.title, step1Result.content, currentConversationHistory)
            currentConversationHistory = step2Result.conversation_history || []
            results.push({
              url: step2Result.url,
              title: step2Result.title,
              content: step2Result.result,
              step: step2Result.step,
              conversation_history: currentConversationHistory
            });

            // Step 3 (if prompt is provided)
            if (step3Prompt) {
              const step3Result = await processUrl(url, 3, step2Result.title, step2Result.content, currentConversationHistory)
              currentConversationHistory = step3Result.conversation_history || []
              results.push({
                url: step3Result.url,
                title: step3Result.title,
                content: step3Result.result,
                step: step3Result.step,
                conversation_history: currentConversationHistory
              });
            }
          }
      
          // 所有步骤完成后，一次性更新conversation
          setConversation(prev => [
            ...prev,
            {
              prompt,
              step2Prompt,
              step3Prompt,
              results,
            },
          ])
      
        } catch (error) {
          setConversation(prev => [
            ...prev,
            {
              prompt,
              step2Prompt,
              step3Prompt,
              results: [
                {
                  url,
                  title: '处理失败',
                  content: '',
                  step: 0,
                  error: `处理 ${url} 时出错: ${error instanceof Error ? error.message : String(error)}`,
                },
              ],
            },
          ])
        }

        // Update progress after each URL is processed
        setProgress(((i + 1) / totalUrls) * 100)
      }
    } catch {
      if (shouldStop) {
        return; // 如果是因为终止导致的错误，直接返回
      }
      // 处理其他错误...
    } finally {
      clearInterval(timer)
      setIsLoading(false)
      setShouldStop(false)
    }
  }

  const handleStop = () => {
    // 保存当前输入数据
    saveInputsToLocalStorage({
      urls,
      prompt,
      step2Prompt,
      step3Prompt,
      selectedModel
    });
    
    setShouldStop(true);
    setIsLoading(false);
    setProgress(0);
    setCurrentUrlIndex(0);
    setCurrentStep('');
    setElapsedTime(0);
    
    toast({
      title: "已终止处理",
      description: "所有操作已停止，正在刷新页面...",
    });

    // 短暂延迟后刷新页面，确保 toast 消息能够显示
    setTimeout(() => {
      window.location.reload();
    }, 1000);
  };

  const handleCopyAllResults = () => {
    const allContent = conversation.map((turn: ConversationTurn, index: number) => {
      const header = `链接 ${index + 1}\n`;
      const prompts = [
        `第一步：${turn.prompt}`,
        turn.step2Prompt ? `第二步：${turn.step2Prompt}` : '',
        turn.step3Prompt ? `第三步：${turn.step3Prompt}` : '',
      ].filter(Boolean).join('\n');

      const results = Array.from(new Set(turn.results.map(r => r.url)))
        .map(url => {
          const urlResults = turn.results.filter(r => r.url === url);
          const title = urlResults[0]?.title;
          const urlContent = `\n标题：${title}\n链接：${url}\n`;
          
          const stepsContent = urlResults
            .map(result => {
              if (result.error) {
                return `错误: ${result.error}`;
              }
              return `步骤 ${result.step}:\n${result.content}`;
            })
            .join('\n\n');
          
          return urlContent + stepsContent;
        })
        .join('\n\n---\n\n');

      return `${header}\n${prompts}\n\n${results}`;
    }).join('\n\n================\n\n');

    navigator.clipboard.writeText(allContent).then(() => {
      toast({
        title: "已复制全部内容",
        description: "所有生成结果已成功复制到剪贴板",
      })
    }).catch(err => {
      console.error('复制失败:', err)
      toast({
        title: "复制失败",
        description: "请手动复制内容",
        variant: "destructive",
      })
    })
  }

  const handleRefresh = () => {
    window.location.reload()
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      toast({
        title: "已复制到剪贴板",
        description: "内容已成功复制",
      })
    }).catch(err => {
      console.error('复制失败:', err)
      toast({
        title: "复制失败",
        description: "请手动复制内容",
        variant: "destructive",
      })
    })
  }

  return (
    <div className={`flex flex-col md:flex-row min-h-screen ${conversation.length > 0 ? 'justify-between' : 'justify-center'}`}>
      <div className={`p-4 ${conversation.length > 0 ? 'md:w-1/2' : 'w-full max-w-2xl'}`}>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle className="text-2xl font-bold">爆款文案生成器</CardTitle>
            <Button
              onClick={handleRefresh}
              variant="outline"
              size="icon"
              aria-label="刷新页面"
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label htmlFor="urls" className="block text-sm font-medium text-gray-700 mb-1">
                输入URL（每行一个）
              </label>
              <textarea
                ref={urlsRef}
                id="urls"
                value={urls}
                onChange={(e) => setUrls(e.target.value)}
                placeholder="https://example.com"
                className="w-full p-2 border rounded-md resize-none"
                style={{ minHeight: '75px', maxHeight: '300px', overflowY: 'auto' }}
              />
            </div>
            <div>
              <label htmlFor="prompt" className="block text-sm font-medium text-gray-700 mb-1">
                第一步提示词
              </label>
              <textarea
                ref={useAutoResizeTextArea(prompt)}
                id="prompt"
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                placeholder="在此输入您的第一步提示词"
                className="w-full p-2 border rounded-md resize-none"
                style={{ minHeight: '75px', maxHeight: '300px', overflowY: 'auto' }}
              />
            </div>
            <div>
              <label htmlFor="step2Prompt" className="block text-sm font-medium text-gray-700 mb-1">
                第二步提示词（可选）
              </label>
              <textarea
                ref={useAutoResizeTextArea(step2Prompt)}
                id="step2Prompt"
                value={step2Prompt}
                onChange={(e) => setStep2Prompt(e.target.value)}
                placeholder="在此输入您的第二步提示词（可选）"
                className="w-full p-2 border rounded-md resize-none"
                style={{ minHeight: '75px', maxHeight: '300px', overflowY: 'auto' }}
              />
            </div>
            <div>
              <label htmlFor="step3Prompt" className="block text-sm font-medium text-gray-700 mb-1">
                第三步提示词（可选）
              </label>
              <textarea
                ref={useAutoResizeTextArea(step3Prompt)}
                id="step3Prompt"
                value={step3Prompt}
                onChange={(e) => setStep3Prompt(e.target.value)}
                placeholder="在此输入您的第三步提示词（可选）"
                className="w-full p-2 border rounded-md resize-none"
                style={{ minHeight: '75px', maxHeight: '300px', overflowY: 'auto' }}
              />
            </div>
            <div>
              <label htmlFor="model-select" className="block text-sm font-medium text-gray-700 mb-1">
                选择模型
              </label>
              <Select value={selectedModel} onValueChange={setSelectedModel}>
                <SelectTrigger id="model-select" className="w-full">
                  <SelectValue placeholder="选择模型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="gpt-4o">GPT-4o</SelectItem>
                  <SelectItem value="gpt-3.5-turbo">GPT-3.5-Turbo</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Button 
              onClick={handleSubmit} 
              disabled={isLoading}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition duration-300"
            >
              {isLoading ? '处理中...' : '提交'}
            </Button>
            {isLoading && (
              <div className="mt-4">
                <Progress value={progress} className="w-full" />
                <div className="flex justify-between items-center mt-2">
                  <p className="text-center">{Math.round(progress)}% 完成</p>
                  <Button 
                    onClick={handleStop}
                    variant="destructive"
                    size="sm"
                    className="ml-2"
                  >
                    终止运行
                  </Button>
                </div>
                <p className="text-left mt-1">正在处理第 {currentUrlIndex} 个URL: {currentStep}</p>
                <p className="text-left">运行时间: {elapsedTime} 秒</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
      {conversation.length > 0 && (
        <div id="chat-container" className="md:w-1/2 p-4">
          <Card className="max-w-full">
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="text-xl font-semibold">对话历史</CardTitle>
              <Button
                onClick={handleCopyAllResults}
                className="bg-blue-600 hover:bg-blue-700 text-white"
                size="sm"
              >
                <Copy className="h-4 w-4 mr-2" />
                复制全部结果
              </Button>
            </CardHeader>
            <CardContent>
              <ScrollArea className="md:h-[calc(100vh-150px)] max-h-[600px]">
                {conversation.map((turn, turnIndex) => (
                  <div key={turnIndex} className="mb-8 border-b pb-4">
                    <h3 className="font-bold text-lg mb-2">链接 {turnIndex + 1}生成结果</h3>
                    <p className="mb-2"><strong>第一步：</strong>{turn.prompt}</p>
                    {turn.step2Prompt && <p className="mb-2"><strong>第二步：</strong>{turn.step2Prompt}</p>}
                    {turn.step3Prompt && <p className="mb-2"><strong>第三步：</strong>{turn.step3Prompt}</p>}
                    <Tabs defaultValue="all">
                      <TabsList className="mb-4">
                        <TabsTrigger value="all" className="px-4 py-2 rounded-md">全 ({turn.results.length})</TabsTrigger>
                        {Array.from(new Set(turn.results.map(r => r.url))).map((url, index) => (
                          <TabsTrigger key={index} value={`${index}`} className="px-4 py-2 rounded-md">
                            {index + 1}
                          </TabsTrigger>
                        ))}
                      </TabsList>
                      <TabsContent value="all">
                        {Array.from(new Set(turn.results.map(r => r.url))).map((url, index) => (
                          <div key={index} className="mb-4 p-4 border rounded-md shadow-sm">
                            <div className="break-words">
                              <h4 className="font-bold text-md mb-2">{turn.results.find(r => r.url === url)?.title}</h4>
                              <a href={url} className="text-blue-500 hover:underline block mb-2 break-all" target="_blank" rel="noopener noreferrer">
                                {url}
                              </a>
                            </div>
                            {turn.results.filter(r => r.url === url).map((result, stepIndex) => (
                              <div key={stepIndex} className="mb-4">
                                {result.error ? (
                                  <div className="flex items-center text-red-500">
                                    <AlertCircle className="mr-2" />
                                    <span>{result.error}</span>
                                  </div>
                                ) : (
                                  <div className="relative pr-12">
                                    <h5 className="font-semibold">步骤 {result.step}</h5>
                                    <CollapsibleText content={result.content} />
                                    <Button
                                      onClick={() => copyToClipboard(result.content)}
                                      className="absolute top-0 right-0 p-2"
                                      variant="outline"
                                      size="icon"
                                    >
                                      <Copy className="h-4 w-4" />
                                      <span className="sr-only">复制内容</span>
                                    </Button>
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                        ))}
                      </TabsContent>
                      {Array.from(new Set(turn.results.map(r => r.url))).map((url, index) => (
                        <TabsContent key={index} value={`${index}`}>
                          <div className="p-4 border rounded-md shadow-sm">
                            <h4 className="font-bold text-md mb-2">{turn.results.find(r => r.url === url)?.title}</h4>
                            <a href={url} className="text-blue-500 hover:underline block mb-2" target="_blank" rel="noopener noreferrer">
                              {url}
                            </a>
                            {turn.results.filter(r => r.url === url).map((result, stepIndex) => (
                              <div key={stepIndex} className="mb-4 relative">
                                {result.error ? (
                                  <div className="flex items-center text-red-500">
                                    <AlertCircle className="mr-2" />
                                    <span>{result.error}</span>
                                  </div>
                                ) : (
                                  <>
                                    <h5 className="font-semibold">步骤 {result.step}</h5>
                                    <CollapsibleText content={result.content} />
                                    <Button
                                      onClick={() => copyToClipboard(result.content)}
                                      className="absolute top-0 right-0 p-2"
                                      variant="outline"
                                      size="icon"
                                    >
                                      <Copy className="h-4 w-4" />
                                      <span className="sr-only">复制内容</span>
                                    </Button>
                                  </>
                                )}
                              </div>
                            ))}
                          </div>
                        </TabsContent>
                      ))}
                    </Tabs>
                  </div>
                ))}
              </ScrollArea>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}